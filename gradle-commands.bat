@echo off
REM Gradle Commands Helper Script
REM This script ensures you always run gradle commands from the correct directory

echo ========================================
echo Gradle Commands Helper
echo ========================================
echo Current directory: %CD%
echo.

REM Check if we're in the root project directory
if not exist "settings.gradle.kts" (
    echo ERROR: You must run this script from the root project directory!
    echo Looking for settings.gradle.kts file...
    echo.
    if exist "app\build.gradle.kts" (
        echo Found app directory. You're probably in the wrong directory.
        echo Please run this script from the parent directory.
    )
    pause
    exit /b 1
)

echo Available commands:
echo.
echo 1. Build entire project: gradle-commands.bat build
echo 2. Build app only: gradle-commands.bat app-build  
echo 3. Clean project: gradle-commands.bat clean
echo 4. Run tests: gradle-commands.bat test
echo 5. Generate wrapper: gradle-commands.bat wrapper
echo 6. Assemble debug APK: gradle-commands.bat debug
echo 7. Show all tasks: gradle-commands.bat tasks
echo.

if "%1"=="" (
    echo Please specify a command. Example: gradle-commands.bat build
    pause
    exit /b 1
)

if "%1"=="build" (
    echo Running: ./gradlew build
    ./gradlew build
    goto end
)

if "%1"=="app-build" (
    echo Running: ./gradlew app:build
    ./gradlew app:build
    goto end
)

if "%1"=="clean" (
    echo Running: ./gradlew clean
    ./gradlew clean
    goto end
)

if "%1"=="test" (
    echo Running: ./gradlew test
    ./gradlew test
    goto end
)

if "%1"=="wrapper" (
    echo Running: ./gradlew wrapper
    ./gradlew wrapper
    goto end
)

if "%1"=="debug" (
    echo Running: ./gradlew app:assembleDebug
    ./gradlew app:assembleDebug
    goto end
)

if "%1"=="tasks" (
    echo Running: ./gradlew tasks
    ./gradlew tasks
    goto end
)

echo Unknown command: %1
echo Please use one of the available commands listed above.

:end
echo.
echo Command completed.
pause
