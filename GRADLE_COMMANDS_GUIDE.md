# Gradle Commands Guide - NEVER GET ERRORS AGAIN!

## 🚨 IMPORTANT: Always run commands from the ROOT directory!

**ROOT DIRECTORY**: `C:/Users/<USER>/OneDrive/Desktop/chattt/` (where `settings.gradle.kts` exists)

## ✅ CORRECT Commands (Use These!)

### Method 1: Use the Helper Scripts (RECOMMENDED)
```bash
# For PowerShell users:
.\gradle-commands.ps1 wrapper
.\gradle-commands.ps1 build
.\gradle-commands.ps1 app-build
.\gradle-commands.ps1 clean

# For Command Prompt users:
gradle-commands.bat wrapper
gradle-commands.bat build
gradle-commands.bat app-build
gradle-commands.bat clean
```

### Method 2: Direct Gradle Commands
```bash
# Make sure you're in the root directory first!
cd C:/Users/<USER>/OneDrive/Desktop/chattt

# Then run these commands:
./gradlew wrapper              # ✅ Generate/update wrapper
./gradlew build               # ✅ Build entire project
./gradlew app:build           # ✅ Build app module only
./gradlew app:assembleDebug   # ✅ Build debug APK
./gradlew clean               # ✅ Clean project
./gradlew app:clean           # ✅ Clean app module
./gradlew tasks               # ✅ Show all available tasks
./gradlew app:tasks           # ✅ Show app-specific tasks
```

## ❌ WRONG Commands (NEVER Use These!)

```bash
./gradlew app:wrapper         # ❌ WRONG - wrapper task doesn't exist in subprojects
cd app && ./gradlew wrapper   # ❌ WRONG - running from wrong directory
```

## 🔍 How to Check You're in the Right Directory

Before running any gradle command, verify you're in the root directory:

```bash
# Check current directory
pwd

# Look for these files (they should exist):
ls settings.gradle.kts        # This file MUST exist in root
ls gradlew                    # Gradle wrapper script
ls app/build.gradle.kts       # App module build file
```

## 🛠️ Quick Fix if You Get Lost

If you're ever unsure about your location:

```bash
# Navigate to the correct root directory
cd C:/Users/<USER>/OneDrive/Desktop/chattt

# Verify you're in the right place
ls settings.gradle.kts

# If the file exists, you're good to go!
```

## 📋 Common Tasks Reference

| Task | Command | Description |
|------|---------|-------------|
| Update Wrapper | `./gradlew wrapper` | Updates Gradle wrapper files |
| Build Project | `./gradlew build` | Builds entire project |
| Build App | `./gradlew app:build` | Builds only the app module |
| Clean | `./gradlew clean` | Cleans build artifacts |
| Debug APK | `./gradlew app:assembleDebug` | Creates debug APK |
| Release APK | `./gradlew app:assembleRelease` | Creates release APK |
| Run Tests | `./gradlew test` | Runs all tests |
| List Tasks | `./gradlew tasks` | Shows available tasks |

## 🎯 Remember These Rules

1. **ALWAYS** run commands from the root directory (`C:/Users/<USER>/OneDrive/Desktop/chattt/`)
2. **NEVER** use `app:wrapper` - wrapper task only exists at root level
3. **USE** the helper scripts to avoid mistakes
4. **CHECK** for `settings.gradle.kts` file to confirm you're in the right directory

## 🚀 Quick Start

1. Open terminal/PowerShell
2. Navigate to root: `cd C:/Users/<USER>/OneDrive/Desktop/chattt`
3. Use helper script: `.\gradle-commands.ps1 wrapper`
4. Or use direct command: `./gradlew wrapper`

**You will NEVER get the wrapper error again if you follow this guide!**
