param(
    [Parameter(Mandatory=$false)]
    [string]$Command
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Gradle Commands Helper" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Check if we're in the root project directory
if (-not (Test-Path "settings.gradle.kts")) {
    Write-Host "ERROR: You must run this script from the root project directory!" -ForegroundColor Red
    Write-Host "Looking for settings.gradle.kts file..." -ForegroundColor Yellow
    Write-Host ""
    
    if (Test-Path "app\build.gradle.kts") {
        Write-Host "Found app directory. You're probably in the wrong directory." -ForegroundColor Yellow
        Write-Host "Please run this script from the parent directory." -ForegroundColor Yellow
    }
    
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Available commands:" -ForegroundColor Green
Write-Host ""
Write-Host "1. Build entire project: .\gradle-commands.ps1 build" -ForegroundColor White
Write-Host "2. Build app only: .\gradle-commands.ps1 app-build" -ForegroundColor White
Write-Host "3. Clean project: .\gradle-commands.ps1 clean" -ForegroundColor White
Write-Host "4. Run tests: .\gradle-commands.ps1 test" -ForegroundColor White
Write-Host "5. Generate wrapper: .\gradle-commands.ps1 wrapper" -ForegroundColor White
Write-Host "6. Assemble debug APK: .\gradle-commands.ps1 debug" -ForegroundColor White
Write-Host "7. Show all tasks: .\gradle-commands.ps1 tasks" -ForegroundColor White
Write-Host ""

if (-not $Command) {
    Write-Host "Please specify a command. Example: .\gradle-commands.ps1 build" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

switch ($Command.ToLower()) {
    "build" {
        Write-Host "Running: ./gradlew build" -ForegroundColor Green
        & ./gradlew build
    }
    "app-build" {
        Write-Host "Running: ./gradlew app:build" -ForegroundColor Green
        & ./gradlew app:build
    }
    "clean" {
        Write-Host "Running: ./gradlew clean" -ForegroundColor Green
        & ./gradlew clean
    }
    "test" {
        Write-Host "Running: ./gradlew test" -ForegroundColor Green
        & ./gradlew test
    }
    "wrapper" {
        Write-Host "Running: ./gradlew wrapper" -ForegroundColor Green
        & ./gradlew wrapper
    }
    "debug" {
        Write-Host "Running: ./gradlew app:assembleDebug" -ForegroundColor Green
        & ./gradlew app:assembleDebug
    }
    "tasks" {
        Write-Host "Running: ./gradlew tasks" -ForegroundColor Green
        & ./gradlew tasks
    }
    default {
        Write-Host "Unknown command: $Command" -ForegroundColor Red
        Write-Host "Please use one of the available commands listed above." -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Command completed." -ForegroundColor Green
Read-Host "Press Enter to exit"
